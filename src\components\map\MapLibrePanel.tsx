import React, { useEffect, useRef, useState, useCallback } from 'react';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { useIncidentStore } from '@/store/incidentStore';
import { useResponseStore } from '@/store/responseStore';
import { useFilterStore } from '@/store/filterStore';
import { Incident, Response } from '@/types/incident';
import { isValidCoordinates, getSafeCoordinates } from '@/utils/mapUtils';
import {
  defaultPerformanceConfig,
  PerformanceConfig,
  MarkerPool,
  filterByViewport,
  debounce,
  cleanupUnusedResources
} from '@/utils/mapPerformance';
import Card from '@/components/ui/Card';

// Import tactical map components
import MapLibreToolbar from './MapLibreToolbar';
import MapLibreMarkers from './MapLibreMarkers';
import MapLibreDrawing from './MapLibreDrawing';
import MapLibreContextMenu from './MapLibreContextMenu';
import TacticalLegend from './TacticalLegend';
import ViewshedAnalysis from './ViewshedAnalysis';
import UnifiedSymbolManager from '../settings/UnifiedSymbolManager';

// Add tactical popup styles
const tacticalPopupStyles = `
  .tactical-popup-container .maplibregl-popup-content {
    background: rgba(26, 30, 35, 0.95);
    border: 1px solid #4a5568;
    border-radius: 8px;
    padding: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    min-width: 280px;
  }

  .tactical-popup-container .maplibregl-popup-tip {
    border-top-color: rgba(26, 30, 35, 0.95);
  }

  .tactical-popup {
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .tactical-popup .popup-header {
    background: rgba(45, 55, 72, 0.8);
    padding: 8px 12px;
    border-bottom: 1px solid #4a5568;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tactical-popup .popup-header h3 {
    margin: 0;
    font-size: 13px;
    font-weight: bold;
    color: #f7fafc;
  }

  .tactical-popup .severity-badge,
  .tactical-popup .status-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
  }

  .tactical-popup .severity-critical { background: #e53e3e; color: white; }
  .tactical-popup .severity-high { background: #dd6b20; color: white; }
  .tactical-popup .severity-medium { background: #d69e2e; color: white; }
  .tactical-popup .severity-low { background: #38a169; color: white; }

  .tactical-popup .status-active { background: #38a169; color: white; }
  .tactical-popup .status-completed { background: #3182ce; color: white; }
  .tactical-popup .status-cancelled { background: #e53e3e; color: white; }

  .tactical-popup .popup-content {
    padding: 12px;
  }

  .tactical-popup .grid-info {
    margin-bottom: 8px;
  }

  .tactical-popup .grid-info div {
    margin-bottom: 4px;
    font-size: 11px;
  }

  .tactical-popup .description {
    margin-bottom: 12px;
    font-size: 11px;
    line-height: 1.4;
  }

  .tactical-popup .popup-button {
    width: 100%;
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
  }

  .tactical-popup .popup-button:hover {
    background: #4a5568;
    border-color: #718096;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = tacticalPopupStyles;
  document.head.appendChild(styleElement);
}

interface MapLibrePanelProps {
  className?: string;
}

interface MapState {
  center: [number, number];
  zoom: number;
  bearing: number;
  pitch: number;
}

const MapLibrePanel: React.FC<MapLibrePanelProps> = ({ className = '' }) => {
  // Map instance and container refs
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<maplibregl.Map | null>(null);
  const markerPool = useRef<MarkerPool>(new MarkerPool());

  // State management
  const { filteredIncidents, selectIncident } = useIncidentStore();
  const { responses, selectResponse } = useResponseStore();
  const { filters } = useFilterStore();

  // Map state
  const [mapState, setMapState] = useState<MapState>({
    center: [69.3451, 30.3753], // Pakistan center (lng, lat for MapLibre)
    zoom: 5,
    bearing: 0,
    pitch: 0
  });

  // UI state
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [activeBaseLayer, setActiveBaseLayer] = useState<string>('satellite');
  const [showResponses, setShowResponses] = useState<boolean>(true);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    lngLat: [number, number];
  } | null>(null);

  // Performance configuration
  const [performanceConfig] = useState<PerformanceConfig>(defaultPerformanceConfig);

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current || map.current) return;

    try {
      // Create MapLibre map instance
      map.current = new maplibregl.Map({
        container: mapContainer.current,
        style: getBaseLayerStyle(activeBaseLayer),
        center: mapState.center,
        zoom: mapState.zoom,
        bearing: mapState.bearing,
        pitch: mapState.pitch,
        antialias: true,
        preserveDrawingBuffer: true,
        attributionControl: false
      });

      // Add attribution control
      map.current.addControl(new maplibregl.AttributionControl({
        compact: true
      }), 'bottom-right');

      // Map event handlers
      map.current.on('load', () => {
        setIsMapLoaded(true);
        console.log('MapLibre map loaded successfully');
      });

      map.current.on('error', (e) => {
        console.error('MapLibre error:', e);
      });

      // Update map state on move
      const updateMapState = debounce(() => {
        if (!map.current) return;

        const center = map.current.getCenter();
        setMapState({
          center: [center.lng, center.lat],
          zoom: map.current.getZoom(),
          bearing: map.current.getBearing(),
          pitch: map.current.getPitch()
        });
      }, performanceConfig.debounceDelay);

      map.current.on('moveend', updateMapState);
      map.current.on('zoomend', updateMapState);

      // Context menu handler
      map.current.on('contextmenu', (e) => {
        e.preventDefault();
        const { point, lngLat } = e;
        setContextMenu({
          x: point.x,
          y: point.y,
          lngLat: [lngLat.lng, lngLat.lat]
        });
      });

      // Click handler to close context menu
      map.current.on('click', () => {
        setContextMenu(null);
      });

    } catch (error) {
      console.error('Failed to initialize MapLibre map:', error);
    }

    // Cleanup function
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
    };
  }, []);

  // Update base layer when changed
  useEffect(() => {
    if (!map.current || !isMapLoaded) return;

    try {
      map.current.setStyle(getBaseLayerStyle(activeBaseLayer));
    } catch (error) {
      console.error('Failed to update base layer:', error);
    }
  }, [activeBaseLayer, isMapLoaded]);

  // Performance optimization: cleanup unused resources periodically
  useEffect(() => {
    if (!map.current) return;

    const cleanup = setInterval(() => {
      if (map.current) {
        cleanupUnusedResources(map.current);
      }
    }, 30000); // Cleanup every 30 seconds

    return () => clearInterval(cleanup);
  }, []);

  // Get base layer style configuration
  const getBaseLayerStyle = (layerType: string): string | maplibregl.StyleSpecification => {
    switch (layerType) {
      case 'satellite':
        return {
          version: 8,
          sources: {
            'google-satellite': {
              type: 'raster',
              tiles: [
                'https://mt0.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt1.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt2.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}',
                'https://mt3.google.com/vt/lyrs=s,h&x={x}&y={y}&z={z}'
              ],
              tileSize: 256,
              attribution: '&copy; Google Maps'
            }
          },
          layers: [
            {
              id: 'google-satellite',
              type: 'raster',
              source: 'google-satellite'
            }
          ]
        };

      case 'terrain':
        return {
          version: 8,
          sources: {
            'google-terrain': {
              type: 'raster',
              tiles: [
                'https://mt0.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt1.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt2.google.com/vt/lyrs=p&x={x}&y={y}&z={z}',
                'https://mt3.google.com/vt/lyrs=p&x={x}&y={y}&z={z}'
              ],
              tileSize: 256,
              attribution: '&copy; Google Maps'
            }
          },
          layers: [
            {
              id: 'google-terrain',
              type: 'raster',
              source: 'google-terrain'
            }
          ]
        };

      case 'streets':
        return {
          version: 8,
          sources: {
            'osm': {
              type: 'raster',
              tiles: [
                'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'https://b.tile.openstreetmap.org/{z}/{x}/{y}.png',
                'https://c.tile.openstreetmap.org/{z}/{x}/{y}.png'
              ],
              tileSize: 256,
              attribution: '&copy; OpenStreetMap contributors'
            }
          },
          layers: [
            {
              id: 'osm',
              type: 'raster',
              source: 'osm'
            }
          ]
        };

      default:
        return 'https://demotiles.maplibre.org/style.json';
    }
  };

  // Handle incident selection
  const handleIncidentSelect = useCallback((incidentId: string) => {
    selectIncident(incidentId);
  }, [selectIncident]);

  // Handle response selection
  const handleResponseSelect = useCallback((responseId: string) => {
    selectResponse(responseId);
  }, [selectResponse]);

  // Toolbar state management
  const [toolbarState, setToolbarState] = useState({
    viewshedVisible: false,
    legendVisible: false,
    symbolsVisible: false
  });

  // Handle base layer change
  const handleBaseLayerChange = useCallback((layerType: string) => {
    setActiveBaseLayer(layerType);
  }, []);

  // Handle context menu close
  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle toolbar state changes
  const handleToolbarStateChange = useCallback((newState: Partial<typeof toolbarState>) => {
    setToolbarState(prev => ({ ...prev, ...newState }));
  }, []);

  return (
    <Card className={`relative h-full w-full overflow-hidden ${className}`}>
      {/* Map container */}
      <div
        ref={mapContainer}
        className="absolute inset-0 w-full h-full"
        style={{ background: '#1a1e23' }}
      />

      {/* Loading indicator */}
      {!isMapLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75 z-50">
          <div className="text-white text-lg font-mono">
            Initializing Tactical Map System...
          </div>
        </div>
      )}

      {/* Map components - only render when map is loaded */}
      {isMapLoaded && map.current && (
        <>
          {/* Four-corner toolbar system */}
          <MapLibreToolbar
            map={map.current}
            activeBaseLayer={activeBaseLayer}
            onBaseLayerChange={handleBaseLayerChange}
            mapState={mapState}
            onToolbarStateChange={handleToolbarStateChange}
          />

          {/* Markers and data visualization */}
          <MapLibreMarkers
            map={map.current}
            incidents={filteredIncidents}
            responses={showResponses ? responses : []}
            onIncidentSelect={handleIncidentSelect}
            onResponseSelect={handleResponseSelect}
            markerPool={markerPool.current}
            performanceConfig={performanceConfig}
          />

          {/* Drawing tools */}
          <MapLibreDrawing
            map={map.current}
          />

          {/* Integrated tactical components - rendered conditionally based on toolbar state */}
          <TacticalLegend
            position="bottom-left"
            incidents={filteredIncidents}
            responses={responses}
            isVisible={toolbarState.legendVisible}
            className="z-[350]" // Tactical legend layer
          />

          <UnifiedSymbolManager
            isOpen={toolbarState.symbolsVisible}
            onClose={() => setToolbarState(prev => ({ ...prev, symbolsVisible: false }))}
            mode="manager"
            className="z-[500]" // Modal layer
          />

          <ViewshedAnalysis
            map={map.current}
            isVisible={toolbarState.viewshedVisible}
            className="z-[400]" // Analysis layer
          />

          {/* Context menu */}
          {contextMenu && (
            <MapLibreContextMenu
              x={contextMenu.x}
              y={contextMenu.y}
              lngLat={contextMenu.lngLat}
              map={map.current}
              onClose={handleContextMenuClose}
            />
          )}
        </>
      )}
    </Card>
  );
};

// Wrapper component for compatibility
const MapLibrePanelWrapper: React.FC = () => {
  return <MapLibrePanel />;
};

export default MapLibrePanelWrapper;
export { MapLibrePanel };
